package com.swhd.agent.service.account.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自助充值源表实体类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_account_oceanengine_customer_recharge_source")
public class AccountOceanengineCustomerRechargeSource extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "充值源标题")
    private String title;

    @Schema(description = "客户ID列表")
    private String customerIds;

    @Schema(description = "状态 0-关闭 1-开启")
    private Integer state;

}
