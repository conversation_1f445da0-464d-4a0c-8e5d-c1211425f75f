package com.swhd.agent.service.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceAddParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceUpdateParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeSource;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeSourceMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeSourceService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 自助充值源表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeSourceServiceImpl extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeSourceMapper, AccountOceanengineCustomerRechargeSource> implements AccountOceanengineCustomerRechargeSourceService {

    @Override
    public IPage<AccountOceanengineCustomerRechargeSource> page(AccountOceanengineCustomerRechargeSourcePageParam param) {
        return lambdaQuery()
                .like(Func.isNotEmpty(param.getTitle()), AccountOceanengineCustomerRechargeSource::getTitle, param.getTitle())
                .and(CollectionUtil.isNotEmpty(param.getCustomerIds()), wrapper -> {
                    for (int i = 0; i < param.getCustomerIds().size(); i++) {
                        Long customerId = param.getCustomerIds().get(i);
                        if (i == 0) {
                            wrapper.apply("JSON_CONTAINS(customer_ids, JSON_ARRAY({0}))", customerId);
                        } else {
                            wrapper.or().apply("JSON_CONTAINS(customer_ids, JSON_ARRAY({0}))", customerId);
                        }
                    }
                })
                .eq(Func.isNotEmpty(param.getState()), AccountOceanengineCustomerRechargeSource::getState, param.getState())
                .orderByDesc(AccountOceanengineCustomerRechargeSource::getCreateTime)
                .orderByDesc(AccountOceanengineCustomerRechargeSource::getId)
                .page(convertToPage(param));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineCustomerRechargeSourceAddParam param) {
        // 校验 title 唯一性
        checkTitleUnique(param.getTitle(), null);

        AccountOceanengineCustomerRechargeSource entity = Func.copy(param, AccountOceanengineCustomerRechargeSource.class);
        // 将客户ID列表转换为JSON字符串存储
        if (CollectionUtil.isNotEmpty(param.getCustomerIds())) {
            entity.setCustomerIds(Func.toJson(param.getCustomerIds()));
        }

        boolean result = save(entity);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineCustomerRechargeSourceUpdateParam param) {
        // 校验 title 唯一性（排除当前记录）
        checkTitleUnique(param.getTitle(), param.getId());

        AccountOceanengineCustomerRechargeSource entity = Func.copy(param, AccountOceanengineCustomerRechargeSource.class);
        // 将客户ID列表转换为JSON字符串存储
        if (CollectionUtil.isNotEmpty(param.getCustomerIds())) {
            entity.setCustomerIds(Func.toJson(param.getCustomerIds()));
        }

        boolean result = updateById(entity);
        return RspHd.status(result);
    }

    /**
     * 校验标题唯一性
     *
     * @param title     标题
     * @param excludeId 排除的ID（更新时使用）
     */
    private void checkTitleUnique(String title, Long excludeId) {
        if (Func.isEmpty(title)) {
            return;
        }

        long count = lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeSource::getTitle, title)
                .ne(Func.isNotEmpty(excludeId), AccountOceanengineCustomerRechargeSource::getId, excludeId)
                .count();

        if (count > 0) {
            throw new ServiceException("充值源标题已存在");
        }
    }

}
