package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeSourceClient;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceAddParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeSourceResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * 自助充值源表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerRechargeSource")
public class AccountOceanengineCustomerRechargeSourceController {

    private final AccountOceanengineCustomerRechargeSourceClient accountOceanengineCustomerRechargeSourceClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCustomerRechargeSourceResult>> page(@RequestBody @Valid AccountOceanengineCustomerRechargeSourcePageParam param) {
        return accountOceanengineCustomerRechargeSourceClient.page(param);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<AccountOceanengineCustomerRechargeSourceResult> getById(@RequestParam("id") Long id) {
        return accountOceanengineCustomerRechargeSourceClient.getById(id);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceAddParam param) {
        return accountOceanengineCustomerRechargeSourceClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceUpdateParam param) {
        return accountOceanengineCustomerRechargeSourceClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return accountOceanengineCustomerRechargeSourceClient.removeByIds(ids);
    }

}
