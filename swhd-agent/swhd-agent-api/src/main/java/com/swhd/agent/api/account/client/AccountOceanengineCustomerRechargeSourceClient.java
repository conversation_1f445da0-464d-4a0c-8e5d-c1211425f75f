package com.swhd.agent.api.account.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceAddParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeSourceResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanengineCustomerRechargeSourceClient.BASE_PATH)
public interface AccountOceanengineCustomerRechargeSourceClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/customer/recharge/source";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanengineCustomerRechargeSourceResult>> page(@RequestBody @Valid AccountOceanengineCustomerRechargeSourcePageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanengineCustomerRechargeSourceResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanengineCustomerRechargeSourceResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
